<template>
  <HerbitProfessionalLayout
    title="Create Assessment"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <!-- Form Card -->
    <FormCard color="cyan">
          <form @submit.prevent="createAssessment" class="space-y-6">
              <!-- Assessment Name -->
              <div>
                <Label for="assessmentName" class="text-gray-300">Assessment Name</Label>
                <Input
                  id="assessmentName"
                  name="assessmentName"
                  v-model="assessmentName"
                  type="text"
                  autocomplete="off"
                  placeholder="e.g. DevOps Basics"
                  required
                />
              </div>

              <!-- Assessment Description -->
              <div>
                <Label for="assessmentDescription" class="text-gray-300">Assessment Description</Label>
                <textarea
                  id="assessmentDescription"
                  name="assessmentDescription"
                  v-model="assessmentDescription"
                  autocomplete="off"
                  placeholder="e.g. A comprehensive assessment of DevOps fundamentals including CI/CD pipelines, containerization, and infrastructure automation"
                  required
                  class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent resize-y min-h-[100px]"
                ></textarea>
                <div class="mt-1 text-xs text-gray-400">
                  Provide a detailed description of what this assessment covers (minimum 20 characters)
                </div>
              </div>

              <!-- Skill Selection (Multiple) -->
              <div>
                <Label class="text-gray-300">Select Skills</Label>
                <div class="mb-2 text-xs text-gray-400">You can select multiple skills for this assessment</div>
                <div class="relative skill-dropdown-container">
                  <button
                    @click="toggleSkillDropdown"
                    type="button"
                    class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent flex justify-between items-center hover:bg-gray-700 transition-colors"
                  >
                    <span v-if="selectedSkillIds.length === 0" class="text-gray-400">Select skills...</span>
                    <span v-else class="text-white">{{ selectedSkillIds.length }} skill(s) selected</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Dropdown Menu -->
                  <div
                    v-if="showSkillDropdown"
                    class="absolute z-10 mt-1 w-full max-h-60 overflow-y-auto bg-gray-800 border border-gray-700 rounded-lg shadow-lg"
                  >
                    <div
                      v-for="skill in skills"
                      :key="skill.id"
                      class="flex items-center px-4 py-2 border-b border-gray-700 last:border-b-0 hover:bg-gray-700/50"
                    >
                      <input
                        type="checkbox"
                        :id="`skill-${skill.id}`"
                        :name="`skill-${skill.id}`"
                        :value="skill.id"
                        v-model="selectedSkillIds"
                        autocomplete="off"
                        class="h-4 w-4 text-cyan-500 focus:ring-cyan-500 border-gray-700 bg-gray-800 rounded"
                      />
                      <Label :for="`skill-${skill.id}`" class="ml-2 text-sm text-gray-300 cursor-pointer flex-1">
                        {{ skill.name }}
                      </Label>
                    </div>
                  </div>
                </div>

                <!-- Selected Skills Display -->
                <div v-if="selectedSkillIds.length > 0" class="mt-2 flex flex-wrap gap-2">
                  <div
                    v-for="skillId in selectedSkillIds"
                    :key="skillId"
                    class="inline-flex items-center bg-cyan-900/50 text-cyan-200 text-xs px-2 py-1 rounded-lg"
                  >
                    {{ getSkillName(skillId) }}
                    <button
                      @click="removeSkill(skillId)"
                      type="button"
                      class="ml-1 text-cyan-300 hover:text-white hover:bg-cyan-900/30 rounded px-1 transition-colors"
                    >
                      &times;
                    </button>
                  </div>
                </div>
                <div v-if="selectedSkillIds.length === 0" class="mt-2 text-xs text-red-400">
                  Please select at least one skill
                </div>
              </div>

              <!-- Assessment Duration -->
              <div>
                <Label for="assessmentDuration" class="text-gray-300">Assessment Duration (minutes)</Label>
                <div class="flex items-center">
                  <Input
                    type="number"
                    id="assessmentDuration"
                    name="assessmentDuration"
                    v-model="assessmentDuration"
                    min="5"
                    max="180"
                    autocomplete="off"
                    class="w-32"
                    required
                  />
                </div>
                <div class="mt-1 text-xs text-gray-400">
                  Set the time limit for completing this assessment
                </div>
              </div>

              <!-- Question Selection Mode -->
              <div>
                <Label class="text-gray-300">Question Selection Mode</Label>
                <div class="flex space-x-4">
                  <div class="flex items-center">
                    <input
                      type="radio"
                      id="dynamicMode"
                      name="questionSelectionMode"
                      value="dynamic"
                      v-model="questionSelectionMode"
                      class="h-4 w-4 text-cyan-500 focus:ring-cyan-500 border-gray-700 bg-gray-800"
                    />
                    <Label for="dynamicMode" class="ml-2 text-sm text-gray-300">
                      Dynamic
                      <span class="block text-xs text-gray-400">Questions randomly selected for each session</span>
                    </Label>
                  </div>
                  <div class="flex items-center">
                    <input
                      type="radio"
                      id="fixedMode"
                      name="questionSelectionMode"
                      value="fixed"
                      v-model="questionSelectionMode"
                      class="h-4 w-4 text-cyan-500 focus:ring-cyan-500 border-gray-700 bg-gray-800"
                    />
                    <Label for="fixedMode" class="ml-2 text-sm text-gray-300">
                      Fixed
                      <span class="block text-xs text-gray-400">Same questions for all sessions</span>
                    </Label>
                  </div>
                </div>
                <div class="mt-2 text-xs text-indigo-300 bg-indigo-900/20 p-2 rounded border border-indigo-800/30">
                  <p><strong>Note:</strong></p>
                  <ul class="list-disc list-inside mt-1 space-y-1">
                    <li><strong>Dynamic mode:</strong> Questions are randomly selected from the skill's question pool for each session.</li>
                    <li><strong>Fixed mode:</strong> You can select specific questions below that will be used for all sessions.</li>
                  </ul>
                </div>
              </div>
              
              <!-- Fixed Questions Selection (Only shown when Fixed mode is selected) -->
              <div v-if="questionSelectionMode === 'fixed'" class="mt-4 border-t border-gray-700 pt-4">
                <h3 class="text-cyan-400 font-medium mb-3">Select Fixed Questions</h3>
                
                <!-- Manual Question Number Input -->
                <div>
                  <Label class="text-gray-300">Question Distribution</Label>
                  <div class="grid grid-cols-3 gap-4 mt-2">
                    <div>
                      <Label class="text-xs text-green-400">Easy (min: 6)</Label>
                      <Input
                        id="easyQuestions"
                        name="easyQuestions"
                        type="number"
                        v-model.number="manualQuestionCounts.easy"
                        min="6"
                        autocomplete="off"
                        placeholder="6"
                      />
                    </div>
                    <div>
                      <Label class="text-xs text-yellow-400">Intermediate (min: 6)</Label>
                      <Input
                        id="intermediateQuestions"
                        name="intermediateQuestions"
                        type="number"
                        v-model.number="manualQuestionCounts.intermediate"
                        min="6"
                        autocomplete="off"
                        placeholder="6"
                      />
                    </div>
                    <div>
                      <Label class="text-xs text-red-400">Advanced (min: 8)</Label>
                      <Input
                        id="advancedQuestions"
                        name="advancedQuestions"
                        type="number"
                        v-model.number="manualQuestionCounts.advanced"
                        min="8"
                        autocomplete="off"
                        placeholder="8"
                      />
                    </div>
                  </div>
                  <div class="mt-2 text-xs text-gray-400">
                    <p><strong>Total Questions:</strong> {{ getTotalManualQuestions() }}</p>
                    <p><strong>Minimum Requirements:</strong> Easy: 6, Intermediate: 6, Advanced: 8 (Total: 20+)</p>
                  </div>
                </div>
                
                <!-- Selected Questions Summary -->
                <div class="mt-4">
                  <div class="flex justify-between items-center mb-1">
                    <Label class="text-gray-300">Selected Questions</Label>
                    <span class="text-xs text-cyan-300">
                      {{ getSelectedQuestionCount() }} selected
                    </span>
                  </div>
                  <div class="bg-gray-800/50 border border-gray-700 rounded-lg p-3 min-h-[60px]">
                    <div v-if="getSelectedQuestionCount() === 0" class="text-gray-500 text-sm italic">
                      No questions selected. Select questions from the list below.
                    </div>
                    <div v-else class="flex flex-wrap gap-2">
                      <div
                        v-for="id in getSelectedQuestionIds()"
                        :key="id"
                        class="flex items-center bg-cyan-900/40 text-cyan-200 text-xs px-2 py-1 rounded"
                      >
                        <span>ID: {{ id }}</span>
                        <Button
                          @click="removeQuestionFromSelection(id)"
                          variant="ghost"
                          size="xs"
                          class="ml-2 text-cyan-300 hover:text-cyan-100"
                        >
                          ×
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Available Questions -->
                <div v-if="availableQuestions.length > 0" class="mt-4">
                  <div class="flex justify-between items-center mb-2">
                    <div class="flex items-center space-x-3">
                      <h3 class="text-sm font-medium text-gray-300">Available Questions</h3>
                      <!-- Random Selection Button -->
                      <Button
                        @click.prevent="selectRandomQuestions"
                        variant="generalAction"
                        size="skillButton"
                        :disabled="availableQuestions.length === 0 || !selectedSkillIds.length"
                        title="Randomly select questions based on assessment requirements"
                      >
                        <span class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 00-3.7-3.7 48.678 48.678 0 00-7.324 0 4.006 4.006 0 00-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3l-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 003.7 3.7 48.656 48.656 0 007.324 0 4.006 4.006 0 003.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3l-3 3" />
                          </svg>
                          Random Select
                        </span>
                      </Button>
                    </div>
                    <div class="flex space-x-2">
                      <!-- Filter by difficulty -->
                      <select
                        id="difficultyFilter"
                        name="difficultyFilter"
                        v-model="difficultyFilter"
                        autocomplete="off"
                        class="text-xs px-2 py-1 bg-gray-800 border border-gray-700 rounded text-white focus:outline-none focus:ring-1 focus:ring-cyan-500"
                      >
                        <option value="all">All Difficulties</option>
                        <option value="easy">Easy</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="advanced">Advanced</option>
                      </select>

                      <!-- Search input -->
                      <div class="relative">
                        <Input
                          id="searchQuery"
                          name="searchQuery"
                          v-model="searchQuery"
                          type="text"
                          autocomplete="off"
                          placeholder="Search questions..."
                          class="text-xs pl-7 pr-2 py-1 w-40"
                        />
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 absolute left-2 top-1.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <div class="bg-gray-800/50 border border-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
                    <div v-if="filteredQuestions.length === 0" class="text-center py-4 text-gray-400">
                      No questions match your filters.
                    </div>
                    <div v-else class="space-y-4">
                      <div v-for="question in filteredQuestions" :key="question.que_id"
                           class="p-3 border border-gray-700 rounded-lg hover:border-cyan-500/50 transition-all"
                           :class="{ 'border-cyan-500/50 bg-cyan-900/10': isQuestionSelected(question.que_id) }">
                        <div class="flex justify-between">
                          <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs rounded-full"
                                  :class="{
                                    'bg-green-900/50 text-green-400': question.level === 'easy',
                                    'bg-yellow-900/50 text-yellow-400': question.level === 'intermediate',
                                    'bg-red-900/50 text-red-400': question.level === 'advanced'
                                  }">
                              {{ question.level.charAt(0).toUpperCase() + question.level.slice(1) }}
                            </span>
                            <span class="text-gray-400 text-sm">ID: {{ question.que_id }}</span>
                            <span class="text-gray-400 text-sm">{{ question.skill_name }}</span>
                          </div>
                          <Button
                            @click="addQuestionToSelection(question.que_id)"
                            variant="generalAction"
                            size="skillButton"
                            :class="isQuestionSelected(question.que_id)
                              ? 'bg-cyan-700/70 text-cyan-200 hover:bg-cyan-600/70'
                              : 'bg-cyan-900/50 text-cyan-400 hover:bg-cyan-800/50'"
                          >
                            {{ isQuestionSelected(question.que_id) ? 'Selected' : 'Select' }}
                          </Button>
                        </div>
                        <div class="mt-2 text-white text-sm">{{ question.question }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- No questions available message -->
                <div v-else-if="selectedSkillIds.length > 0" class="mt-4 text-center py-6 bg-gray-800/50 border border-gray-700 rounded-lg">
                  <p class="text-gray-400">Loading questions for selected skills...</p>
                  <div v-if="isLoadingQuestions" class="mt-2 flex justify-center">
                    <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-cyan-500"></div>
                  </div>
                </div>
                
                <!-- No skills selected message -->
                <div v-else class="mt-4 text-center py-6 bg-gray-800/50 border border-gray-700 rounded-lg">
                  <p class="text-gray-400">Please select at least one skill to view available questions.</p>
                </div>
              </div>

              <!-- Loading indicator -->
              <div v-if="isLoading" class="flex justify-center items-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-cyan-500"></div>
                <span class="ml-3 text-gray-300">Creating assessment...</span>
              </div>

              <!-- Error/Success message -->
              <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
                <AlertDescription>{{ message }}</AlertDescription>
              </Alert>

              <!-- Assessment details after creation -->
              <div v-if="createdAssessmentDetails" class="mt-4 bg-gray-800/70 border border-gray-700 rounded-lg p-4">
                <h3 class="text-cyan-400 font-medium mb-2">Assessment Details:</h3>
                <div class="space-y-2 text-sm text-gray-300">
                  <p><span class="text-gray-400">Assessment Name:</span> {{ createdAssessmentDetails.assessment_base_name }}</p>
                  <p><span class="text-gray-400">Assessment Description:</span> {{ createdAssessmentDetails.assessment_description || assessmentDescription }}</p>
                  <p><span class="text-gray-400">Assessment ID:</span> {{ createdAssessmentDetails.assessment_id }}</p>
                  <p><span class="text-gray-400">Duration:</span> {{ createdAssessmentDetails.duration || assessmentDuration }} minutes</p>
                  <p><span class="text-gray-400">Question Selection Mode:</span>
                    <span class="capitalize">{{ createdAssessmentDetails.question_selection_mode }}</span>
                    <span v-if="createdAssessmentDetails.question_selection_mode === 'fixed'" class="ml-2 text-xs text-indigo-300">
                      (Go to "Add Fixed Questions" to assign specific questions)
                    </span>
                  </p>
                  <div v-if="createdAssessmentDetails.skill_ids && createdAssessmentDetails.skill_ids.length > 0">
                    <p class="text-gray-400 mb-1">Skills:</p>
                    <div class="flex flex-wrap gap-2 mb-2">
                      <span
                        v-for="skillId in createdAssessmentDetails.skill_ids"
                        :key="skillId"
                        class="inline-block bg-cyan-900/30 text-cyan-200 text-xs px-2 py-1 rounded-lg"
                      >
                        {{ getSkillName(skillId) }}
                      </span>
                    </div>
                  </div>
                  <p v-if="createdAssessmentDetails.total_questions_available">
                    <span class="text-gray-400">Available Questions:</span> {{ createdAssessmentDetails.total_questions_available }}
                  </p>
                  <div class="mt-4">
                    <p class="text-cyan-400 font-medium">Next Steps:</p>
                    <p class="text-gray-300">
                      Use the "Generate Sessions" menu to create session codes for this assessment.
                    </p>
                    <div class="mt-2">
                      <Button
                        @click="navigateTo('/generate-sessions')"
                        variant="generalAction"
                        size="skillButton"
                      >
                        Go to Generate Sessions
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Submit button -->
              <div class="flex justify-end">
                <Button
                  type="submit"
                  variant="assessmentGenerate"
                  size="skillButton"
                  :disabled="isLoading"
                >
                  <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create Assessment
                  </span>
                </Button>
              </div>
            </form>
    </FormCard>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { safeAddEventListener } from '@/utils/domHelpers';
import { HerbitProfessionalLayout } from '@/components/layout';
import { FormCard } from '@/components/ui/form-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

const router = useRouter();
const navigateTo = (path) => {
  router.push(path);
};

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } = useMessageHandler();

// Form data
const assessmentName = ref('');
const assessmentDescription = ref('');
const selectedSkillIds = ref([]); // Changed to array for multiple selection
const questionSelectionMode = ref('dynamic'); // Default to dynamic mode
const assessmentDuration = ref(30); // Default duration in minutes
const skills = ref([]);
const isLoading = ref(false);
const createdAssessmentDetails = ref(null);
const showSkillDropdown = ref(false); // For dropdown toggle

// Fixed questions data
const questionIds = ref(''); // Comma-separated list of selected question IDs
const availableQuestions = ref([]);
const isLoadingQuestions = ref(false);
const searchQuery = ref('');
const difficultyFilter = ref('all');
const manualQuestionCounts = ref({
  easy: 6,
  intermediate: 6,
  advanced: 8
});

// Helper functions for skill management
const getSkillName = (skillId) => {
  const skill = skills.value.find(s => s.id === skillId);
  return skill ? skill.name : `Skill ${skillId}`;
};

const removeSkill = (skillId) => {
  selectedSkillIds.value = selectedSkillIds.value.filter(id => id !== skillId);
};

const toggleSkillDropdown = () => {
  showSkillDropdown.value = !showSkillDropdown.value;
};

// Close dropdown when clicking outside
const closeDropdownOnOutsideClick = (event) => {
  if (showSkillDropdown.value && event.target && event.target.closest) {
    const container = event.target.closest('.skill-dropdown-container');
    if (!container) {
      showSkillDropdown.value = false;
    }
  }
};

// Fetch skills from API
const fetchSkills = async () => {
  try {
    isLoading.value = true;
    const response = await api.admin.getSkills();
    skills.value = response.data;
  } catch (error) {
    logError(error, 'fetchSkills');
    setErrorMessage(getErrorMessage(error, 'Failed to fetch skills'));
  } finally {
    isLoading.value = false;
  }
};

// Fetch questions for selected skills
const fetchQuestionsForSkills = async () => {
  if (!selectedSkillIds.value.length || questionSelectionMode.value !== 'fixed') {
    availableQuestions.value = [];
    return;
  }

  isLoadingQuestions.value = true;
  clearMessage();

  try {
    // Create a temporary array to store all questions
    let allQuestions = [];

    // Fetch questions for each selected skill
    for (const skillId of selectedSkillIds.value) {
      const response = await api.admin.getSkillQuestions(skillId);
      const skillQuestions = response.data.questions || [];
      
      // Add skill name to each question for better filtering/display
      const skillName = getSkillName(skillId);
      const questionsWithSkillName = skillQuestions.map(q => ({
        ...q,
        skill_name: skillName
      }));
      
      allQuestions = [...allQuestions, ...questionsWithSkillName];
    }

    // Remove duplicates (in case questions are shared across skills)
    const uniqueQuestions = [];
    const questionIds = new Set();
    
    for (const question of allQuestions) {
      if (!questionIds.has(question.que_id)) {
        questionIds.add(question.que_id);
        uniqueQuestions.push(question);
      }
    }

    availableQuestions.value = uniqueQuestions;
  } catch (error) {
    logError(error, 'fetchQuestionsForSkills');
    setErrorMessage(getErrorMessage(error, 'Failed to fetch questions for selected skills'));
  } finally {
    isLoadingQuestions.value = false;
  }
};

// Create assessment via API
const createAssessment = async () => {
  // Enhanced validation
  if (!assessmentName.value || !assessmentDescription.value || selectedSkillIds.value.length === 0) {
    setErrorMessage('Please fill in all required fields and select at least one skill');
    return;
  }

  // Additional validation
  if (assessmentName.value.trim().length < 3) {
    setErrorMessage('Assessment name must be at least 3 characters long');
    return;
  }

  if (assessmentDescription.value.trim().length < 20) {
    setErrorMessage('Assessment description must be at least 20 characters long');
    return;
  }

  if (selectedSkillIds.value.length > 10) {
    setErrorMessage('Please select no more than 10 skills for an assessment');
    return;
  }

  // Validate fixed questions if in fixed mode
  if (questionSelectionMode.value === 'fixed') {
    // Validate minimum requirements
    if (manualQuestionCounts.value.easy < 6) {
      setErrorMessage('Easy questions must be at least 6. You can add more than 6 if needed.');
      return;
    }
    if (manualQuestionCounts.value.intermediate < 6) {
      setErrorMessage('Intermediate questions must be at least 6. You can add more than 6 if needed.');
      return;
    }
    if (manualQuestionCounts.value.advanced < 8) {
      setErrorMessage('Advanced questions must be at least 8. You can add more than 8 if needed.');
      return;
    }

    // Check if selected questions match the manual counts
    const totalSelected = getSelectedQuestionCount();
    const totalRequired = getTotalManualQuestions();

    if (totalSelected === 0) {
      setErrorMessage('Please select questions for your fixed assessment.');
      return;
    }

    if (totalSelected !== totalRequired) {
      setErrorMessage(`You have selected ${totalSelected} questions but specified ${totalRequired} questions in the manual entry. Please adjust either your selection or the manual counts to match.`);
      return;
    }
  }

  isLoading.value = true;
  clearMessage();
  createdAssessmentDetails.value = null;

  try {
    // Get the primary skill for the topic (using the first selected skill)
    const primarySkillId = selectedSkillIds.value[0];
    const primarySkill = skills.value.find(skill => skill.id === primarySkillId);

    if (!primarySkill) {
      throw new Error('Selected skill not found');
    }

    // Get current username (in a real app, this would come from auth)
    const username = localStorage.getItem('username') || 'admin_user';

    // Call the API to create the quiz/assessment
    const response = await api.admin.createAssessment({
      quiz_name: assessmentName.value,
      topic: assessmentDescription.value, // Use the new description field instead of skill description
      user_id: username,
      skill_ids: selectedSkillIds.value.map(id => parseInt(id)), // Convert all selected skill IDs to integers
      question_selection_mode: questionSelectionMode.value,
      duration: parseInt(assessmentDuration.value), // Add duration in minutes
      create_single_assessment: true // Create only one assessment instead of mock and final
    });

    // Extract data from response
    const responseData = response.data;

    // Store the response details for display
    createdAssessmentDetails.value = responseData;

    // If fixed mode, add the selected questions to the assessment
    if (questionSelectionMode.value === 'fixed' && responseData.assessment_id) {
      try {
        const selectedIds = getSelectedQuestionIds();
        
        // Call the API to add fixed questions
        const fixedQuestionsResponse = await api.admin.addFinalQuestions({
          assessment_id: parseInt(responseData.assessment_id),
          question_ids: selectedIds,
          quiz_name: assessmentName.value,
          question_distribution: {
            easy: manualQuestionCounts.value.easy,
            intermediate: manualQuestionCounts.value.intermediate,
            advanced: manualQuestionCounts.value.advanced,
            total: getTotalManualQuestions()
          }
        });
        
        // Update success message to include questions
        setSuccessMessage(`Successfully created "${assessmentName.value}" assessment with ${selectedIds.length} fixed questions!`);
      } catch (fixedQuestionsError) {
        logError(fixedQuestionsError, 'addFixedQuestions');
        setErrorMessage(`Assessment created, but failed to add fixed questions: ${getErrorMessage(fixedQuestionsError)}`);
      }
    } else {
      setSuccessMessage(`Successfully created "${assessmentName.value}" assessment!`);
    }

    // Reset form after success
    assessmentName.value = '';
    assessmentDescription.value = '';
    selectedSkillIds.value = [];
    questionIds.value = '';
    availableQuestions.value = [];
    assessmentDuration.value = 30; // Reset to default duration
    questionSelectionMode.value = 'dynamic'; // Reset to default mode

  } catch (error) {
    logError(error, 'createAssessment');
    setErrorMessage(getErrorMessage(error, 'An unexpected error occurred while creating the assessment'));
    createdAssessmentDetails.value = null;
  } finally {
    isLoading.value = false;
  }
};

// Store cleanup function for event listener
let cleanupEventListener = null;

// Watch for changes in selected skills or question selection mode
watch([selectedSkillIds, questionSelectionMode], async ([newSkillIds, newMode], [oldSkillIds, oldMode]) => {
  // If skills changed or mode changed to fixed, fetch questions
  if (
    (JSON.stringify(newSkillIds) !== JSON.stringify(oldSkillIds) && newSkillIds.length > 0) || 
    (newMode === 'fixed' && oldMode !== 'fixed')
  ) {
    await fetchQuestionsForSkills();
  }
  
  // If mode changed from fixed to dynamic, clear questions
  if (newMode !== 'fixed' && oldMode === 'fixed') {
    availableQuestions.value = [];
    questionIds.value = '';
  }
});

onMounted(() => {
  fetchSkills();

  // Add event listener for closing dropdown when clicking outside
  cleanupEventListener = safeAddEventListener(document, 'click', closeDropdownOnOutsideClick);
});

// Clean up event listener when component is unmounted
onUnmounted(() => {
  if (cleanupEventListener) {
    cleanupEventListener();
  }
});

// Computed property for filtered questions
const filteredQuestions = computed(() => {
  return availableQuestions.value.filter(question => {
    // Filter by difficulty
    if (difficultyFilter.value !== 'all' && question.level !== difficultyFilter.value) {
      return false;
    }

    // Filter by search query
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase();
      return (
        question.question.toLowerCase().includes(query) ||
        question.que_id.toString().includes(query) ||
        (question.skill_name && question.skill_name.toLowerCase().includes(query))
      );
    }

    return true;
  });
});

// Helper methods for question selection
const getSelectedQuestionIds = () => {
  return questionIds.value.split(',')
    .map(id => id.trim())
    .filter(id => id)
    .map(id => parseInt(id));
};

const getSelectedQuestionCount = () => {
  return getSelectedQuestionIds().length;
};

// Helper method to calculate total manual questions
const getTotalManualQuestions = () => {
  return (manualQuestionCounts.value.easy || 0) +
         (manualQuestionCounts.value.intermediate || 0) +
         (manualQuestionCounts.value.advanced || 0);
};

const isQuestionSelected = (questionId) => {
  return getSelectedQuestionIds().includes(questionId);
};

const addQuestionToSelection = (questionId) => {
  const selectedIds = getSelectedQuestionIds();

  if (selectedIds.includes(questionId)) {
    // Remove the question if already selected
    removeQuestionFromSelection(questionId);
  } else {
    // Add the question if not already selected
    selectedIds.push(questionId);
    questionIds.value = selectedIds.join(', ');
  }
};

const removeQuestionFromSelection = (questionId) => {
  const selectedIds = getSelectedQuestionIds();
  const updatedIds = selectedIds.filter(id => id !== questionId);
  questionIds.value = updatedIds.join(', ');
};

// Randomly select questions based on assessment requirements
const selectRandomQuestions = () => {
  // Validate manual question counts meet minimum requirements (allow more than minimum)
  if (manualQuestionCounts.value.easy < 6) {
    setErrorMessage('Easy questions must be at least 6. You can specify more than 6 if needed.');
    return;
  }
  if (manualQuestionCounts.value.intermediate < 6) {
    setErrorMessage('Intermediate questions must be at least 6. You can specify more than 6 if needed.');
    return;
  }
  if (manualQuestionCounts.value.advanced < 8) {
    setErrorMessage('Advanced questions must be at least 8. You can specify more than 8 if needed.');
    return;
  }

  // Clear current selection
  questionIds.value = '';

  // Get counts from manual input
  const easyCount = manualQuestionCounts.value.easy;
  const intermediateCount = manualQuestionCounts.value.intermediate;
  const advancedCount = manualQuestionCounts.value.advanced;

  // Group available questions by difficulty
  const easyQuestions = availableQuestions.value.filter(q => q.level === 'easy');
  const intermediateQuestions = availableQuestions.value.filter(q => q.level === 'intermediate');
  const advancedQuestions = availableQuestions.value.filter(q => q.level === 'advanced');

  // Check if we have enough questions of each difficulty
  if (easyQuestions.length < easyCount) {
    setErrorMessage(`Not enough easy questions available. Need ${easyCount}, but only have ${easyQuestions.length}.`);
    return;
  }

  if (intermediateQuestions.length < intermediateCount) {
    setErrorMessage(`Not enough intermediate questions available. Need ${intermediateCount}, but only have ${intermediateQuestions.length}.`);
    return;
  }

  if (advancedQuestions.length < advancedCount) {
    setErrorMessage(`Not enough advanced questions available. Need ${advancedCount}, but only have ${advancedQuestions.length}.`);
    return;
  }

  // Helper function to shuffle array
  const shuffleArray = (array) => {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  };

  // Shuffle and select questions by difficulty
  const selectedEasy = shuffleArray([...easyQuestions]).slice(0, easyCount);
  const selectedIntermediate = shuffleArray([...intermediateQuestions]).slice(0, intermediateCount);
  const selectedAdvanced = shuffleArray([...advancedQuestions]).slice(0, advancedCount);

  // Combine all selected questions
  const selectedQuestions = [...selectedEasy, ...selectedIntermediate, ...selectedAdvanced];

  // Update the selection
  questionIds.value = selectedQuestions.map(q => q.que_id).join(', ');

  // Show success message
  setSuccessMessage(`Randomly selected ${selectedQuestions.length} questions based on assessment requirements.`);
};
</script>

<style scoped>
/* No need for animation styles as they're now in HerbitBackground component */
</style>
